#pragma once

#include <string>
#include <map>

class AppConfig {
public:
    AppConfig();
    ~AppConfig();

    // File operations
    bool load_from_file(const std::string& path);
    bool save_to_file(const std::string& path);

    // Configuration getters
    std::string get_string(const std::string& key, const std::string& default_value = "");
    int get_int(const std::string& key, int default_value = 0);
    bool get_bool(const std::string& key, bool default_value = false);
    double get_double(const std::string& key, double default_value = 0.0);

    // Configuration setters
    void set_string(const std::string& key, const std::string& value);
    void set_int(const std::string& key, int value);
    void set_bool(const std::string& key, bool value);
    void set_double(const std::string& key, double value);

    // Utility methods
    bool has_key(const std::string& key) const;
    void remove_key(const std::string& key);
    void clear();

private:
    std::map<std::string, std::string> config_data_;
    std::string config_file_path_;
};
