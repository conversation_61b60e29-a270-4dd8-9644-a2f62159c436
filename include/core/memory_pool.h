#pragma once

#include <memory>
#include <vector>
#include <mutex>
#include <cstddef>

/**
 * Simple memory pool for frequently allocated objects
 * Reduces memory fragmentation and allocation overhead
 */
template<typename T, size_t PoolSize = 100>
class MemoryPool {
public:
    MemoryPool() {
        pool_.reserve(PoolSize);
        for (size_t i = 0; i < PoolSize; ++i) {
            pool_.emplace_back(std::make_unique<T>());
            available_.push_back(pool_.back().get());
        }
    }

    ~MemoryPool() = default;

    // Get an object from the pool
    T* acquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (available_.empty()) {
            // Pool exhausted, create new object
            return new T();
        }
        
        T* obj = available_.back();
        available_.pop_back();
        return obj;
    }

    // Return an object to the pool
    void release(T* obj) {
        if (!obj) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Check if this object belongs to our pool
        bool is_pool_object = false;
        for (const auto& pool_obj : pool_) {
            if (pool_obj.get() == obj) {
                is_pool_object = true;
                break;
            }
        }
        
        if (is_pool_object) {
            // Reset object state if needed
            reset_object(obj);
            available_.push_back(obj);
        } else {
            // Object was created outside pool, delete it
            delete obj;
        }
    }

    // Get pool statistics
    size_t available_count() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return available_.size();
    }

    size_t total_count() const {
        return PoolSize;
    }

    size_t used_count() const {
        return total_count() - available_count();
    }

private:
    std::vector<std::unique_ptr<T>> pool_;
    std::vector<T*> available_;
    mutable std::mutex mutex_;

    // Override this for custom object reset logic
    virtual void reset_object(T* obj) {
        // Default: do nothing
        // Derived classes can override to reset object state
        (void)obj;
    }
};

/**
 * RAII wrapper for memory pool objects
 */
template<typename T>
class PooledObject {
public:
    PooledObject(MemoryPool<T>* pool) : pool_(pool), obj_(pool->acquire()) {}
    
    ~PooledObject() {
        if (pool_ && obj_) {
            pool_->release(obj_);
        }
    }

    // Move constructor
    PooledObject(PooledObject&& other) noexcept 
        : pool_(other.pool_), obj_(other.obj_) {
        other.pool_ = nullptr;
        other.obj_ = nullptr;
    }

    // Move assignment
    PooledObject& operator=(PooledObject&& other) noexcept {
        if (this != &other) {
            if (pool_ && obj_) {
                pool_->release(obj_);
            }
            pool_ = other.pool_;
            obj_ = other.obj_;
            other.pool_ = nullptr;
            other.obj_ = nullptr;
        }
        return *this;
    }

    // Disable copy
    PooledObject(const PooledObject&) = delete;
    PooledObject& operator=(const PooledObject&) = delete;

    T* get() { return obj_; }
    const T* get() const { return obj_; }
    
    T& operator*() { return *obj_; }
    const T& operator*() const { return *obj_; }
    
    T* operator->() { return obj_; }
    const T* operator->() const { return obj_; }

    explicit operator bool() const { return obj_ != nullptr; }

private:
    MemoryPool<T>* pool_;
    T* obj_;
};
