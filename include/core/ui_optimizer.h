#pragma once

#include <gtk/gtk.h>
#include <chrono>
#include <functional>
#include <queue>
#include <mutex>

/**
 * UI optimization utilities for better responsiveness
 */
class UIOptimizer {
public:
    // Singleton pattern
    static UIOptimizer& instance();

    // Batch UI updates to reduce redraws
    void batch_ui_updates(std::function<void()> update_func);
    void flush_batched_updates();

    // Throttle rapid UI updates
    void throttle_update(const std::string& update_id, 
                        std::function<void()> update_func,
                        std::chrono::milliseconds min_interval = std::chrono::milliseconds(16)); // ~60 FPS

    // Defer heavy operations to idle time
    void defer_to_idle(std::function<void()> operation);

    // Optimize widget creation
    static void optimize_widget(GtkWidget* widget);
    static void optimize_container(GtkContainer* container);

    // Memory optimization for GTK widgets
    static void cleanup_widget_resources(GtkWidget* widget);

    // Performance monitoring integration
    void monitor_ui_operation(const std::string& operation, std::function<void()> func);

    // Configuration
    void set_batch_timeout(std::chrono::milliseconds timeout);
    void set_throttle_enabled(bool enabled);

private:
    UIOptimizer() = default;
    ~UIOptimizer() = default;
    UIOptimizer(const UIOptimizer&) = delete;
    UIOptimizer& operator=(const UIOptimizer&) = delete;

    struct ThrottleInfo {
        std::chrono::steady_clock::time_point last_update;
        std::chrono::milliseconds min_interval;
        std::function<void()> pending_func;
        bool has_pending = false;
    };

    std::mutex mutex_;
    std::queue<std::function<void()>> batched_updates_;
    std::map<std::string, ThrottleInfo> throttle_map_;
    std::chrono::milliseconds batch_timeout_{16}; // ~60 FPS
    bool throttle_enabled_ = true;
    guint batch_timer_id_ = 0;

    static gboolean flush_batched_updates_callback(gpointer user_data);
    static gboolean idle_operation_callback(gpointer user_data);
    static gboolean throttled_update_callback(gpointer user_data);
};

/**
 * RAII class for batching UI updates
 */
class UIUpdateBatch {
public:
    UIUpdateBatch();
    ~UIUpdateBatch();

    // Disable copy and move
    UIUpdateBatch(const UIUpdateBatch&) = delete;
    UIUpdateBatch& operator=(const UIUpdateBatch&) = delete;
    UIUpdateBatch(UIUpdateBatch&&) = delete;
    UIUpdateBatch& operator=(UIUpdateBatch&&) = delete;

private:
    UIOptimizer& optimizer_;
};

/**
 * Macros for easy UI optimization
 */
#define UI_BATCH_UPDATES() UIUpdateBatch _batch
#define UI_THROTTLE(id, interval, func) UIOptimizer::instance().throttle_update(id, func, interval)
#define UI_DEFER_TO_IDLE(func) UIOptimizer::instance().defer_to_idle(func)
#define UI_MONITOR(operation, func) UIOptimizer::instance().monitor_ui_operation(operation, func)
