#pragma once

#include <chrono>
#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>

class PerformanceMonitor {
public:
    struct PerformanceData {
        std::string operation;
        std::chrono::milliseconds duration;
        size_t memory_usage_mb;
        double cpu_usage_percent;
        std::chrono::system_clock::time_point timestamp;
    };

    struct PerformanceThresholds {
        static const int MAX_UI_RESPONSE_TIME_MS = 100;
        static const int MAX_MEMORY_USAGE_MB = 100;
        static const int MAX_STARTUP_TIME_MS = 2000;
        static const int MAX_APP_SWITCH_TIME_MS = 200;
        static const int MIN_FPS = 30;
        static const int MAX_DB_QUERY_TIME_MS = 10;
    };

    // Singleton pattern
    static PerformanceMonitor& instance();

    // Timer operations
    void start_timer(const std::string& operation);
    void end_timer(const std::string& operation);
    std::chrono::milliseconds get_last_duration(const std::string& operation) const;

    // Memory monitoring
    void log_memory_usage();
    size_t get_current_memory_usage_mb() const;
    size_t get_peak_memory_usage_mb() const;

    // CPU monitoring
    void log_cpu_usage();
    double get_current_cpu_usage() const;

    // Performance data management
    void record_performance_data(const PerformanceData& data);
    std::vector<PerformanceData> get_performance_history() const;
    std::vector<PerformanceData> get_performance_history(const std::string& operation) const;

    // Threshold checking
    bool check_thresholds() const;
    std::vector<std::string> get_threshold_violations() const;

    // Reporting
    void generate_report() const;
    void generate_report(const std::string& filename) const;
    std::string get_performance_summary() const;

    // Configuration
    void set_monitoring_enabled(bool enabled);
    bool is_monitoring_enabled() const;
    void clear_history();

    // Specialized monitoring
    void monitor_ui_response_time(const std::string& ui_operation, std::chrono::milliseconds duration);
    void monitor_app_startup_time(const std::string& app_name, std::chrono::milliseconds duration);
    void monitor_app_switch_time(const std::string& from_app, const std::string& to_app, std::chrono::milliseconds duration);
    void monitor_database_query(const std::string& query_type, std::chrono::milliseconds duration);

private:
    PerformanceMonitor() = default;
    ~PerformanceMonitor() = default;
    PerformanceMonitor(const PerformanceMonitor&) = delete;
    PerformanceMonitor& operator=(const PerformanceMonitor&) = delete;

    mutable std::mutex mutex_;
    std::map<std::string, std::chrono::steady_clock::time_point> active_timers_;
    std::map<std::string, std::chrono::milliseconds> last_durations_;
    std::vector<PerformanceData> performance_history_;
    
    size_t current_memory_usage_mb_ = 0;
    size_t peak_memory_usage_mb_ = 0;
    double current_cpu_usage_ = 0.0;
    bool monitoring_enabled_ = false;  // Disabled by default for production

    // Helper methods
    size_t calculate_memory_usage() const;
    double calculate_cpu_usage() const;
    void check_and_record_violations() const;
    std::string format_duration(std::chrono::milliseconds duration) const;
    std::string get_timestamp_string() const;
};

// RAII timer helper class
class ScopedTimer {
public:
    explicit ScopedTimer(const std::string& operation);
    ~ScopedTimer();

private:
    std::string operation_;
    PerformanceMonitor& monitor_;
};

// Convenience macros
#define PERF_TIMER(operation) ScopedTimer _timer(operation)
#define PERF_START(operation) PerformanceMonitor::instance().start_timer(operation)
#define PERF_END(operation) PerformanceMonitor::instance().end_timer(operation)
