#include <gtk/gtk.h>
#include "shared/app_launcher.h"
#include "apps/calculator_app.h"
#include "apps/minesweeper_app.h"
#include "apps/music_player_app.h"
#include "apps/video_player_app.h"

static void activate(GtkApplication* app, gpointer user_data) {
    // Create and register apps
    auto& launcher = AppLauncher::instance();

    // Register calculator app
    auto calculator = std::make_unique<CalculatorApp>();
    launcher.register_app(std::move(calculator));

    // Register minesweeper app
    auto minesweeper = std::make_unique<MinesweeperApp>();
    launcher.register_app(std::move(minesweeper));

    // Register music player app
    auto music_player = std::make_unique<MusicPlayerApp>();
    launcher.register_app(std::move(music_player));

    // Register video player app
    auto video_player = std::make_unique<VideoPlayerApp>();
    launcher.register_app(std::move(video_player));

    // Initialize and show launcher
    launcher.initialize(app);
    launcher.show_launcher();
}

int main(int argc, char** argv) {
    GtkApplication* app = gtk_application_new(
        "org.multiapp.suite",
        G_APPLICATION_FLAGS_NONE
    );

    g_signal_connect(app, "activate", G_CALLBACK(activate), NULL);

    int status = g_application_run(G_APPLICATION(app), argc, argv);
    g_object_unref(app);

    return status;
}