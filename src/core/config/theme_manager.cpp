#include "core/theme_manager.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <algorithm>

ThemeManager::ThemeManager()
    : current_theme_("default"), dark_mode_(false), css_provider_(nullptr) {
    css_provider_ = gtk_css_provider_new();
}

ThemeManager::~ThemeManager() {
    if (css_provider_) {
        g_object_unref(css_provider_);
    }
}

ThemeManager& ThemeManager::instance() {
    static ThemeManager instance;
    return instance;
}

bool ThemeManager::load_theme(const std::string& theme_name) {
    std::string css_content = load_css_file(theme_name);
    if (css_content.empty()) {
        std::cerr << "Failed to load theme: " << theme_name << std::endl;
        return false;
    }

    update_css_provider(css_content);
    current_theme_ = theme_name;
    refresh_all_widgets();

    return true;
}

bool ThemeManager::set_dark_mode(bool enabled) {
    dark_mode_ = enabled;
    // For now, just reload the current theme
    // In a full implementation, this would load dark/light variants
    return load_theme(current_theme_);
}

void ThemeManager::refresh_all_widgets() {
    for (GtkWidget* widget : managed_widgets_) {
        if (GTK_IS_WIDGET(widget)) {
            gtk_widget_reset_style(widget);
        }
    }
}

void ThemeManager::register_widget(GtkWidget* widget) {
    if (widget && std::find(managed_widgets_.begin(), managed_widgets_.end(), widget) == managed_widgets_.end()) {
        managed_widgets_.push_back(widget);
    }
}

void ThemeManager::unregister_widget(GtkWidget* widget) {
    auto it = std::find(managed_widgets_.begin(), managed_widgets_.end(), widget);
    if (it != managed_widgets_.end()) {
        managed_widgets_.erase(it);
    }
}

std::string ThemeManager::get_current_theme() const {
    return current_theme_;
}

bool ThemeManager::is_dark_mode() const {
    return dark_mode_;
}

bool ThemeManager::apply_css_to_widget(GtkWidget* widget, const std::string& css_class) {
    if (!widget || !GTK_IS_WIDGET(widget)) {
        return false;
    }

    // Apply CSS class to widget
    gtk_style_context_add_class(gtk_widget_get_style_context(widget), css_class.c_str());
    return true;
}

std::string ThemeManager::get_background_color() const {
    return dark_mode_ ? "#2d2d2d" : "#f5f5f5";
}

std::string ThemeManager::get_foreground_color() const {
    return dark_mode_ ? "#e0e0e0" : "#2c2c2c";
}

std::string ThemeManager::get_accent_color() const {
    return "#007bff";
}

std::string ThemeManager::get_button_bg_color() const {
    return dark_mode_ ? "#404040" : "#ffffff";
}

std::string ThemeManager::get_button_fg_color() const {
    return dark_mode_ ? "#e0e0e0" : "#2c2c2c";
}

void ThemeManager::apply_theme() {
    // Apply the CSS provider to the default screen
    GdkScreen* screen = gdk_screen_get_default();
    if (screen) {
        gtk_style_context_add_provider_for_screen(
            screen,
            GTK_STYLE_PROVIDER(css_provider_),
            GTK_STYLE_PROVIDER_PRIORITY_APPLICATION
        );
    }
}

std::string ThemeManager::load_css_file(const std::string& theme_name) {
    std::string theme_path = "resources/ui/themes/" + theme_name + ".css";

    // Try to find the theme file
    std::ifstream file(theme_path);
    if (!file.is_open()) {
        // Try with absolute path or relative to executable
        char* exe_path = g_file_read_link("/proc/self/exe", NULL);
        if (exe_path) {
            std::string exe_dir = g_path_get_dirname(exe_path);
            theme_path = exe_dir + std::string("/../") + theme_path;
            g_free(exe_path);

            file.open(theme_path);
        }
    }

    if (!file.is_open()) {
        std::cerr << "Could not find theme file: " << theme_path << std::endl;
        return "";
    }

    std::string css_content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
    file.close();

    return css_content;
}

void ThemeManager::update_css_provider(const std::string& css_content) {
    GError* error = NULL;
    gtk_css_provider_load_from_data(css_provider_, css_content.c_str(), -1, &error);

    if (error) {
        std::cerr << "CSS Error: " << error->message << std::endl;
        g_error_free(error);
    } else {
        apply_theme();
    }
}