# Core library CMakeLists.txt
set(CORE_SOURCES
    gtk/app_interface.cpp
    gtk/calculator_button.cpp
    gtk/game_grid.cpp
    gtk/media_controls.cpp
    json/json_database.cpp
    utils/math_parser.cpp
    utils/string_utils.cpp
    utils/performance_monitor.cpp
    utils/ui_optimizer.cpp
    config/theme_manager.cpp
)

set(CORE_HEADERS
    ${CMAKE_SOURCE_DIR}/include/core/app_interface.h
    ${CMAKE_SOURCE_DIR}/include/core/calculator_button.h
    ${CMAKE_SOURCE_DIR}/include/core/game_grid.h
    ${CMAKE_SOURCE_DIR}/include/core/media_controls.h
    ${CMAKE_SOURCE_DIR}/include/core/json_database.h
    ${CMAKE_SOURCE_DIR}/include/core/math_parser.h
    ${CMAKE_SOURCE_DIR}/include/core/string_utils.h
    ${CMAKE_SOURCE_DIR}/include/core/performance_monitor.h
    ${CMAKE_SOURCE_DIR}/include/core/memory_pool.h
    ${CMAKE_SOURCE_DIR}/include/core/ui_optimizer.h
    ${CMAKE_SOURCE_DIR}/include/core/file_utils.h
    ${CMAKE_SOURCE_DIR}/include/core/theme_manager.h
)

add_library(core ${CORE_SOURCES} ${CORE_HEADERS})

target_include_directories(core
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
    PRIVATE
        ${GTK3_INCLUDE_DIRS}
        ${GSTREAMER_INCLUDE_DIRS}
)

target_link_libraries(core
    ${GTK3_LIBRARIES}
    ${GSTREAMER_LIBRARIES}
)

# Set library properties
set_target_properties(core PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    OUTPUT_NAME "multiapp-core"
)

# Install library and headers
install(TARGETS core
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY ${CMAKE_SOURCE_DIR}/include/core/
    DESTINATION include/multiapp/core
    FILES_MATCHING PATTERN "*.h"
)