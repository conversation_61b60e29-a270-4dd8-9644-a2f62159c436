#include "core/performance_monitor.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cstdlib>
#include <unistd.h>
#include <sys/resource.h>

PerformanceMonitor& PerformanceMonitor::instance() {
    static PerformanceMonitor instance;
    return instance;
}

void PerformanceMonitor::start_timer(const std::string& operation) {
    if (!monitoring_enabled_) return;
    
    std::lock_guard<std::mutex> lock(mutex_);
    active_timers_[operation] = std::chrono::steady_clock::now();
}

void PerformanceMonitor::end_timer(const std::string& operation) {
    if (!monitoring_enabled_) return;
    
    auto end_time = std::chrono::steady_clock::now();
    
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = active_timers_.find(operation);
    if (it != active_timers_.end()) {
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - it->second
        );
        
        last_durations_[operation] = duration;
        
        // Record performance data
        PerformanceData data;
        data.operation = operation;
        data.duration = duration;
        data.memory_usage_mb = get_current_memory_usage_mb();
        data.cpu_usage_percent = get_current_cpu_usage();
        data.timestamp = std::chrono::system_clock::now();
        
        performance_history_.push_back(data);
        
        // Remove from active timers
        active_timers_.erase(it);
        
        // Check thresholds
        check_and_record_violations();
    }
}

std::chrono::milliseconds PerformanceMonitor::get_last_duration(const std::string& operation) const {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = last_durations_.find(operation);
    return (it != last_durations_.end()) ? it->second : std::chrono::milliseconds(0);
}

void PerformanceMonitor::log_memory_usage() {
    if (!monitoring_enabled_) return;
    
    current_memory_usage_mb_ = calculate_memory_usage();
    peak_memory_usage_mb_ = std::max(peak_memory_usage_mb_, current_memory_usage_mb_);
}

size_t PerformanceMonitor::get_current_memory_usage_mb() const {
    return const_cast<PerformanceMonitor*>(this)->calculate_memory_usage();
}

size_t PerformanceMonitor::get_peak_memory_usage_mb() const {
    return peak_memory_usage_mb_;
}

void PerformanceMonitor::log_cpu_usage() {
    if (!monitoring_enabled_) return;
    
    current_cpu_usage_ = calculate_cpu_usage();
}

double PerformanceMonitor::get_current_cpu_usage() const {
    return const_cast<PerformanceMonitor*>(this)->calculate_cpu_usage();
}

void PerformanceMonitor::record_performance_data(const PerformanceData& data) {
    if (!monitoring_enabled_) return;
    
    std::lock_guard<std::mutex> lock(mutex_);
    performance_history_.push_back(data);
}

std::vector<PerformanceMonitor::PerformanceData> PerformanceMonitor::get_performance_history() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return performance_history_;
}

std::vector<PerformanceMonitor::PerformanceData> PerformanceMonitor::get_performance_history(const std::string& operation) const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<PerformanceData> filtered;
    
    std::copy_if(performance_history_.begin(), performance_history_.end(),
                 std::back_inserter(filtered),
                 [&operation](const PerformanceData& data) {
                     return data.operation == operation;
                 });
    
    return filtered;
}

bool PerformanceMonitor::check_thresholds() const {
    return get_threshold_violations().empty();
}

std::vector<std::string> PerformanceMonitor::get_threshold_violations() const {
    std::vector<std::string> violations;
    
    // Check recent performance data
    std::lock_guard<std::mutex> lock(mutex_);
    for (const auto& data : performance_history_) {
        if (data.operation.find("ui_") == 0 && 
            data.duration.count() > PerformanceThresholds::MAX_UI_RESPONSE_TIME_MS) {
            violations.push_back("UI response time exceeded: " + data.operation + 
                                " took " + std::to_string(data.duration.count()) + "ms");
        }
        
        if (data.memory_usage_mb > PerformanceThresholds::MAX_MEMORY_USAGE_MB) {
            violations.push_back("Memory usage exceeded: " + std::to_string(data.memory_usage_mb) + "MB");
        }
        
        if (data.operation.find("startup_") == 0 && 
            data.duration.count() > PerformanceThresholds::MAX_STARTUP_TIME_MS) {
            violations.push_back("Startup time exceeded: " + data.operation + 
                                " took " + std::to_string(data.duration.count()) + "ms");
        }
    }
    
    return violations;
}

void PerformanceMonitor::generate_report() const {
    generate_report("/tmp/multiapp_performance_report.txt");
}

void PerformanceMonitor::generate_report(const std::string& filename) const {
    std::ofstream file(filename);
    if (!file.is_open()) {
        // std::cerr << "Failed to open performance report file: " << filename << std::endl;
        return;
    }

    file << get_performance_summary();
    file.close();

    // std::cout << "Performance report generated: " << filename << std::endl;
}

std::string PerformanceMonitor::get_performance_summary() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::stringstream ss;

    ss << "=== MultiApp Performance Report ===\n";
    ss << "Generated: " << get_timestamp_string() << "\n\n";

    ss << "Memory Usage:\n";
    ss << "  Current: " << current_memory_usage_mb_ << " MB\n";
    ss << "  Peak: " << peak_memory_usage_mb_ << " MB\n\n";

    ss << "CPU Usage: " << std::fixed << std::setprecision(2) << current_cpu_usage_ << "%\n\n";

    // Performance history summary
    ss << "Performance History (" << performance_history_.size() << " entries):\n";
    for (const auto& data : performance_history_) {
        ss << "  " << data.operation << ": " << data.duration.count() << "ms"
           << " (Memory: " << data.memory_usage_mb << "MB, CPU: "
           << std::fixed << std::setprecision(1) << data.cpu_usage_percent << "%)\n";
    }

    // Threshold violations
    auto violations = get_threshold_violations();
    if (!violations.empty()) {
        ss << "\nThreshold Violations:\n";
        for (const auto& violation : violations) {
            ss << "  ⚠️  " << violation << "\n";
        }
    } else {
        ss << "\n✅ All performance thresholds met\n";
    }

    return ss.str();
}

void PerformanceMonitor::set_monitoring_enabled(bool enabled) {
    std::lock_guard<std::mutex> lock(mutex_);
    monitoring_enabled_ = enabled;
}

bool PerformanceMonitor::is_monitoring_enabled() const {
    return monitoring_enabled_;
}

void PerformanceMonitor::clear_history() {
    std::lock_guard<std::mutex> lock(mutex_);
    performance_history_.clear();
    last_durations_.clear();
    peak_memory_usage_mb_ = 0;
}

void PerformanceMonitor::monitor_ui_response_time(const std::string& ui_operation, std::chrono::milliseconds duration) {
    if (!monitoring_enabled_) return;

    PerformanceData data;
    data.operation = "ui_" + ui_operation;
    data.duration = duration;
    data.memory_usage_mb = get_current_memory_usage_mb();
    data.cpu_usage_percent = get_current_cpu_usage();
    data.timestamp = std::chrono::system_clock::now();

    record_performance_data(data);
}

void PerformanceMonitor::monitor_app_startup_time(const std::string& app_name, std::chrono::milliseconds duration) {
    if (!monitoring_enabled_) return;

    PerformanceData data;
    data.operation = "startup_" + app_name;
    data.duration = duration;
    data.memory_usage_mb = get_current_memory_usage_mb();
    data.cpu_usage_percent = get_current_cpu_usage();
    data.timestamp = std::chrono::system_clock::now();

    record_performance_data(data);
}

void PerformanceMonitor::monitor_app_switch_time(const std::string& from_app, const std::string& to_app, std::chrono::milliseconds duration) {
    if (!monitoring_enabled_) return;

    PerformanceData data;
    data.operation = "switch_" + from_app + "_to_" + to_app;
    data.duration = duration;
    data.memory_usage_mb = get_current_memory_usage_mb();
    data.cpu_usage_percent = get_current_cpu_usage();
    data.timestamp = std::chrono::system_clock::now();

    record_performance_data(data);
}

void PerformanceMonitor::monitor_database_query(const std::string& query_type, std::chrono::milliseconds duration) {
    if (!monitoring_enabled_) return;

    PerformanceData data;
    data.operation = "db_" + query_type;
    data.duration = duration;
    data.memory_usage_mb = get_current_memory_usage_mb();
    data.cpu_usage_percent = get_current_cpu_usage();
    data.timestamp = std::chrono::system_clock::now();

    record_performance_data(data);
}

size_t PerformanceMonitor::calculate_memory_usage() const {
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        // Convert from KB to MB
        return usage.ru_maxrss / 1024;
    }
    return 0;
}

double PerformanceMonitor::calculate_cpu_usage() const {
    // Simplified CPU usage calculation
    // In a real implementation, you'd want to track CPU time over intervals
    static auto last_time = std::chrono::steady_clock::now();
    static clock_t last_cpu = clock();

    auto current_time = std::chrono::steady_clock::now();
    clock_t current_cpu = clock();

    auto time_diff = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_time);
    clock_t cpu_diff = current_cpu - last_cpu;

    if (time_diff.count() > 0) {
        double cpu_usage = (double(cpu_diff) / CLOCKS_PER_SEC) / (time_diff.count() / 1000.0) * 100.0;
        last_time = current_time;
        last_cpu = current_cpu;
        return std::min(cpu_usage, 100.0);
    }

    return 0.0;
}

void PerformanceMonitor::check_and_record_violations() const {
    // This method is called after recording performance data
    // Violations are checked in get_threshold_violations()
}

std::string PerformanceMonitor::format_duration(std::chrono::milliseconds duration) const {
    return std::to_string(duration.count()) + "ms";
}

std::string PerformanceMonitor::get_timestamp_string() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// ScopedTimer implementation
ScopedTimer::ScopedTimer(const std::string& operation)
    : operation_(operation), monitor_(PerformanceMonitor::instance()) {
    monitor_.start_timer(operation_);
}

ScopedTimer::~ScopedTimer() {
    monitor_.end_timer(operation_);
}
