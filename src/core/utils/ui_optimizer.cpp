#include "core/ui_optimizer.h"
#include "core/performance_monitor.h"
#include <iostream>

UIOptimizer& UIOptimizer::instance() {
    static UIOptimizer instance;
    return instance;
}

void UIOptimizer::batch_ui_updates(std::function<void()> update_func) {
    std::lock_guard<std::mutex> lock(mutex_);
    batched_updates_.push(update_func);
    
    // Start batch timer if not already running
    if (batch_timer_id_ == 0) {
        batch_timer_id_ = g_timeout_add(batch_timeout_.count(), 
                                       flush_batched_updates_callback, 
                                       this);
    }
}

void UIOptimizer::flush_batched_updates() {
    std::queue<std::function<void()>> updates_to_process;
    
    {
        std::lock_guard<std::mutex> lock(mutex_);
        updates_to_process.swap(batched_updates_);
        if (batch_timer_id_ != 0) {
            g_source_remove(batch_timer_id_);
            batch_timer_id_ = 0;
        }
    }
    
    // Process all batched updates
    while (!updates_to_process.empty()) {
        try {
            updates_to_process.front()();
        } catch (const std::exception& e) {
            std::cerr << "Error in batched UI update: " << e.what() << std::endl;
        }
        updates_to_process.pop();
    }
}

void UIOptimizer::throttle_update(const std::string& update_id, 
                                 std::function<void()> update_func,
                                 std::chrono::milliseconds min_interval) {
    if (!throttle_enabled_) {
        update_func();
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    auto now = std::chrono::steady_clock::now();
    auto& throttle_info = throttle_map_[update_id];
    
    auto time_since_last = now - throttle_info.last_update;
    
    if (time_since_last >= min_interval) {
        // Execute immediately
        throttle_info.last_update = now;
        throttle_info.min_interval = min_interval;
        
        // Execute outside of lock to avoid deadlock
        lock.~lock_guard();
        update_func();
    } else {
        // Schedule for later
        throttle_info.pending_func = update_func;
        throttle_info.has_pending = true;
        throttle_info.min_interval = min_interval;
        
        auto delay = min_interval - time_since_last;
        g_timeout_add(std::chrono::duration_cast<std::chrono::milliseconds>(delay).count(),
                     throttled_update_callback,
                     new std::string(update_id));
    }
}

void UIOptimizer::defer_to_idle(std::function<void()> operation) {
    auto* func_ptr = new std::function<void()>(operation);
    g_idle_add(idle_operation_callback, func_ptr);
}

void UIOptimizer::optimize_widget(GtkWidget* widget) {
    if (!widget) return;
    
    // Enable double buffering for smoother rendering
    gtk_widget_set_double_buffered(widget, TRUE);
    
    // Set appropriate size request to avoid unnecessary resizing
    if (GTK_IS_WINDOW(widget)) {
        gtk_window_set_resizable(GTK_WINDOW(widget), FALSE);
    }
    
    // Optimize drawing for specific widget types
    if (GTK_IS_DRAWING_AREA(widget)) {
        // Enable hardware acceleration if available
        gtk_widget_set_app_paintable(widget, TRUE);
    }
}

void UIOptimizer::optimize_container(GtkContainer* container) {
    if (!container) return;
    
    // Set border width to 0 to reduce layout calculations
    gtk_container_set_border_width(container, 0);
    
    // Optimize child widgets
    GList* children = gtk_container_get_children(container);
    for (GList* iter = children; iter != NULL; iter = g_list_next(iter)) {
        GtkWidget* child = GTK_WIDGET(iter->data);
        optimize_widget(child);
        
        if (GTK_IS_CONTAINER(child)) {
            optimize_container(GTK_CONTAINER(child));
        }
    }
    g_list_free(children);
}

void UIOptimizer::cleanup_widget_resources(GtkWidget* widget) {
    if (!widget) return;
    
    // Disconnect all signal handlers to prevent memory leaks
    g_signal_handlers_disconnect_matched(widget, G_SIGNAL_MATCH_DATA, 0, 0, NULL, NULL, NULL);
    
    // Clear any associated data
    g_object_set_data(G_OBJECT(widget), "user_data", NULL);
    
    // If it's a container, cleanup children first
    if (GTK_IS_CONTAINER(widget)) {
        GList* children = gtk_container_get_children(GTK_CONTAINER(widget));
        for (GList* iter = children; iter != NULL; iter = g_list_next(iter)) {
            cleanup_widget_resources(GTK_WIDGET(iter->data));
        }
        g_list_free(children);
    }
}

void UIOptimizer::monitor_ui_operation(const std::string& operation, std::function<void()> func) {
    auto start_time = std::chrono::steady_clock::now();
    
    try {
        func();
    } catch (const std::exception& e) {
        std::cerr << "Error in UI operation '" << operation << "': " << e.what() << std::endl;
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    PerformanceMonitor::instance().monitor_ui_response_time(operation, duration);
}

void UIOptimizer::set_batch_timeout(std::chrono::milliseconds timeout) {
    std::lock_guard<std::mutex> lock(mutex_);
    batch_timeout_ = timeout;
}

void UIOptimizer::set_throttle_enabled(bool enabled) {
    throttle_enabled_ = enabled;
}

gboolean UIOptimizer::flush_batched_updates_callback(gpointer user_data) {
    UIOptimizer* optimizer = static_cast<UIOptimizer*>(user_data);
    optimizer->flush_batched_updates();
    return FALSE; // Don't repeat
}

gboolean UIOptimizer::idle_operation_callback(gpointer user_data) {
    auto* func_ptr = static_cast<std::function<void()>*>(user_data);
    
    try {
        (*func_ptr)();
    } catch (const std::exception& e) {
        std::cerr << "Error in idle operation: " << e.what() << std::endl;
    }
    
    delete func_ptr;
    return FALSE; // Don't repeat
}

gboolean UIOptimizer::throttled_update_callback(gpointer user_data) {
    auto* update_id_ptr = static_cast<std::string*>(user_data);
    std::string update_id = *update_id_ptr;
    delete update_id_ptr;
    
    UIOptimizer& optimizer = UIOptimizer::instance();
    std::lock_guard<std::mutex> lock(optimizer.mutex_);
    
    auto it = optimizer.throttle_map_.find(update_id);
    if (it != optimizer.throttle_map_.end() && it->second.has_pending) {
        auto func = it->second.pending_func;
        it->second.has_pending = false;
        it->second.last_update = std::chrono::steady_clock::now();
        
        // Execute outside of lock
        lock.~lock_guard();
        func();
    }
    
    return FALSE; // Don't repeat
}

// UIUpdateBatch implementation
UIUpdateBatch::UIUpdateBatch() : optimizer_(UIOptimizer::instance()) {
    // Constructor - batching starts automatically when updates are added
}

UIUpdateBatch::~UIUpdateBatch() {
    optimizer_.flush_batched_updates();
}
