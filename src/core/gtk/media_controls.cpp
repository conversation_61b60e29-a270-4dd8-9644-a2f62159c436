#include "core/media_controls.h"

struct _MediaControls {
    GtkBox parent_instance;
    GtkWidget* play_button;
    GtkWidget* pause_button;
    GtkWidget* stop_button;
    GtkWidget* volume_scale;
    GtkWidget* progress_bar;
    GtkWidget* time_label;

    GCallback play_callback;
    GCallback pause_callback;
    GCallback stop_callback;
    GCallback volume_callback;
    GCallback seek_callback;
    gpointer callback_data;
};

G_DEFINE_TYPE(MediaControls, media_controls, GTK_TYPE_BOX)

static void media_controls_init(MediaControls* self) {
    self->play_callback = NULL;
    self->pause_callback = NULL;
    self->stop_callback = NULL;
    self->volume_callback = NULL;
    self->seek_callback = NULL;
    self->callback_data = NULL;

    // Create control buttons
    self->play_button = gtk_button_new_with_label("▶");
    self->pause_button = gtk_button_new_with_label("⏸");
    self->stop_button = gtk_button_new_with_label("⏹");

    // Create volume control
    self->volume_scale = gtk_scale_new_with_range(GTK_ORIENTATION_HORIZONTAL, 0.0, 1.0, 0.1);
    gtk_scale_set_value_pos(GTK_SCALE(self->volume_scale), GTK_POS_LEFT);

    // Create progress bar
    self->progress_bar = gtk_progress_bar_new();
    gtk_progress_bar_set_fraction(GTK_PROGRESS_BAR(self->progress_bar), 0.0);

    // Create time label
    self->time_label = gtk_label_new("00:00 / 00:00");

    // Layout the controls
    GtkWidget* button_box = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 5);
    gtk_box_pack_start(GTK_BOX(button_box), self->play_button, FALSE, FALSE, 0);
    gtk_box_pack_start(GTK_BOX(button_box), self->pause_button, FALSE, FALSE, 0);
    gtk_box_pack_start(GTK_BOX(button_box), self->stop_button, FALSE, FALSE, 0);

    GtkWidget* volume_box = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 5);
    GtkWidget* volume_label = gtk_label_new("Volume:");
    gtk_box_pack_start(GTK_BOX(volume_box), volume_label, FALSE, FALSE, 0);
    gtk_box_pack_start(GTK_BOX(volume_box), self->volume_scale, TRUE, TRUE, 0);

    // Add everything to the main box
    gtk_box_pack_start(GTK_BOX(self), button_box, FALSE, FALSE, 5);
    gtk_box_pack_start(GTK_BOX(self), self->progress_bar, TRUE, TRUE, 5);
    gtk_box_pack_start(GTK_BOX(self), self->time_label, FALSE, FALSE, 5);
    gtk_box_pack_start(GTK_BOX(self), volume_box, FALSE, FALSE, 5);

    // Connect signals
    g_signal_connect(self->play_button, "clicked", G_CALLBACK(on_play_clicked), self);
    g_signal_connect(self->pause_button, "clicked", G_CALLBACK(on_pause_clicked), self);
    g_signal_connect(self->stop_button, "clicked", G_CALLBACK(on_stop_clicked), self);
    g_signal_connect(self->volume_scale, "value-changed", G_CALLBACK(on_volume_changed), self);
    g_signal_connect(self->progress_bar, "value-changed", G_CALLBACK(on_progress_changed), self);
}

static void media_controls_class_init(MediaControlsClass* klass) {
    // Nothing to do here
}

static void on_play_clicked(GtkWidget* widget, gpointer data) {
    MediaControls* controls = MEDIA_MEDIA_CONTROLS(data);
    if (controls->play_callback) {
        ((void (*)(GtkWidget*, gpointer))controls->play_callback)(widget, controls->callback_data);
    }
}

static void on_pause_clicked(GtkWidget* widget, gpointer data) {
    MediaControls* controls = MEDIA_MEDIA_CONTROLS(data);
    if (controls->pause_callback) {
        ((void (*)(GtkWidget*, gpointer))controls->pause_callback)(widget, controls->callback_data);
    }
}

static void on_stop_clicked(GtkWidget* widget, gpointer data) {
    MediaControls* controls = MEDIA_MEDIA_CONTROLS(data);
    if (controls->stop_callback) {
        ((void (*)(GtkWidget*, gpointer))controls->stop_callback)(widget, controls->callback_data);
    }
}

static void on_volume_changed(GtkWidget* widget, gpointer data) {
    MediaControls* controls = MEDIA_MEDIA_CONTROLS(data);
    if (controls->volume_callback) {
        double volume = gtk_range_get_value(GTK_RANGE(widget));
        ((void (*)(GtkWidget*, double, gpointer))controls->volume_callback)(widget, volume, controls->callback_data);
    }
}

static void on_progress_changed(GtkWidget* widget, gpointer data) {
    MediaControls* controls = MEDIA_MEDIA_CONTROLS(data);
    if (controls->seek_callback) {
        double progress = gtk_progress_bar_get_fraction(GTK_PROGRESS_BAR(widget));
        ((void (*)(GtkWidget*, double, gpointer))controls->seek_callback)(widget, progress, controls->callback_data);
    }
}

MediaControls* media_controls_new() {
    return g_object_new(MEDIA_TYPE_CONTROLS, NULL);
}

void media_controls_set_play_callback(MediaControls* controls, GCallback callback, gpointer data) {
    g_return_if_fail(MEDIA_IS_CONTROLS(controls));
    controls->play_callback = callback;
    controls->callback_data = data;
}

void media_controls_set_pause_callback(MediaControls* controls, GCallback callback, gpointer data) {
    g_return_if_fail(MEDIA_IS_CONTROLS(controls));
    controls->pause_callback = callback;
    controls->callback_data = data;
}

void media_controls_set_stop_callback(MediaControls* controls, GCallback callback, gpointer data) {
    g_return_if_fail(MEDIA_IS_CONTROLS(controls));
    controls->stop_callback = callback;
    controls->callback_data = data;
}

void media_controls_set_volume_callback(MediaControls* controls, GCallback callback, gpointer data) {
    g_return_if_fail(MEDIA_IS_CONTROLS(controls));
    controls->volume_callback = callback;
    controls->callback_data = data;
}

void media_controls_set_seek_callback(MediaControls* controls, GCallback callback, gpointer data) {
    g_return_if_fail(MEDIA_IS_CONTROLS(controls));
    controls->seek_callback = callback;
    controls->callback_data = data;
}

void media_controls_update_progress(MediaControls* controls, double progress, const char* time_text) {
    g_return_if_fail(MEDIA_IS_CONTROLS(controls));
    gtk_progress_bar_set_fraction(GTK_PROGRESS_BAR(controls->progress_bar), progress);
    if (time_text) {
        gtk_label_set_text(GTK_LABEL(controls->time_label), time_text);
    }
}

void media_controls_set_volume(MediaControls* controls, double volume) {
    g_return_if_fail(MEDIA_IS_CONTROLS(controls));
    gtk_range_set_value(GTK_RANGE(controls->volume_scale), volume);
}