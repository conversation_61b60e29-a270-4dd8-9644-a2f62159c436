#include "core/calculator_button.h"

G_DEFINE_TYPE(CalculatorButton, calculator_button, GTK_TYPE_BUTTON)

static void calculator_button_init(CalculatorButton* self) {
    self->button_text = NULL;
    self->button_value = NULL;
}

static void calculator_button_finalize(GObject* object) {
    CalculatorButton* self = CALCULATOR_BUTTON(object);
    g_free(self->button_text);
    g_free(self->button_value);
    G_OBJECT_CLASS(calculator_button_parent_class)->finalize(object);
}

static void calculator_button_class_init(CalculatorButtonClass* klass) {
    GObjectClass* object_class = G_OBJECT_CLASS(klass);
    object_class->finalize = calculator_button_finalize;
}

CalculatorButton* calculator_button_new(const char* text, const char* value) {
    CalculatorButton* button = CALCULATOR_BUTTON(g_object_new(CALCULATOR_TYPE_BUTTON, NULL));
    calculator_button_set_value(button, value);
    gtk_button_set_label(GTK_BUTTON(button), text);
    return button;
}

void calculator_button_set_value(CalculatorButton* button, const char* value) {
    g_return_if_fail(CALCULATOR_IS_BUTTON(button));

    if (button->button_value) {
        g_free(button->button_value);
    }

    button->button_value = g_strdup(value);
}

const char* calculator_button_get_value(CalculatorButton* button) {
    g_return_val_if_fail(CALCULATOR_IS_BUTTON(button), NULL);
    return button->button_value;
}