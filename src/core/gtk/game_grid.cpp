#include "core/game_grid.h"

G_DEFINE_TYPE(GameGrid, game_grid, GTK_TYPE_GRID)

static void game_grid_init(GameGrid* self) {
    self->rows = 0;
    self->cols = 0;
    self->cell_clicked_callback = NULL;
    self->callback_data = NULL;
}

static void game_grid_finalize(GObject* object) {
    GameGrid* self = GAME_GRID(object);
    self->cells.clear();
    G_OBJECT_CLASS(game_grid_parent_class)->finalize(object);
}

static void game_grid_class_init(GameGridClass* klass) {
    GObjectClass* object_class = G_OBJECT_CLASS(klass);
    object_class->finalize = game_grid_finalize;
}

static void on_cell_clicked(GtkWidget* widget, gpointer data) {
    GameGrid* grid = GAME_GRID(data);

    // Find the cell coordinates
    for (size_t i = 0; i < grid->cells.size(); ++i) {
        if (grid->cells[i] == widget) {
            int row = i / grid->cols;
            int col = i % grid->cols;

            if (grid->cell_clicked_callback) {
                // Create a simple data structure to pass coordinates
                int* coords = g_new(int, 2);
                coords[0] = row;
                coords[1] = col;

                ((void (*)(GtkWidget*, gpointer))grid->cell_clicked_callback)(widget, coords);
                g_free(coords);
            }
            break;
        }
    }
}

GameGrid* game_grid_new(int rows, int cols) {
    GameGrid* grid = GAME_GRID(g_object_new(GAME_TYPE_GRID, NULL));
    grid->rows = rows;
    grid->cols = cols;

    // Create cells
    for (int row = 0; row < rows; ++row) {
        for (int col = 0; col < cols; ++col) {
            GtkWidget* button = gtk_button_new();
            gtk_widget_set_size_request(button, 30, 30);

            g_signal_connect(button, "clicked", G_CALLBACK(on_cell_clicked), grid);

            gtk_grid_attach(GTK_GRID(grid), button, col, row, 1, 1);
            grid->cells.push_back(button);
        }
    }

    return grid;
}

void game_grid_set_cell_content(GameGrid* grid, int row, int col, const char* content) {
    g_return_if_fail(GAME_IS_GRID(grid));
    g_return_if_fail(row >= 0 && row < grid->rows);
    g_return_if_fail(col >= 0 && col < grid->cols);

    int index = row * grid->cols + col;
    if (index < (int)grid->cells.size()) {
        gtk_button_set_label(GTK_BUTTON(grid->cells[index]), content);
    }
}

void game_grid_set_cell_sensitive(GameGrid* grid, int row, int col, gboolean sensitive) {
    g_return_if_fail(GAME_IS_GRID(grid));
    g_return_if_fail(row >= 0 && row < grid->rows);
    g_return_if_fail(col >= 0 && col < grid->cols);

    int index = row * grid->cols + col;
    if (index < (int)grid->cells.size()) {
        gtk_widget_set_sensitive(grid->cells[index], sensitive);
    }
}

void game_grid_clear_grid(GameGrid* grid) {
    g_return_if_fail(GAME_IS_GRID(grid));

    for (GtkWidget* cell : grid->cells) {
        gtk_button_set_label(GTK_BUTTON(cell), "");
        gtk_widget_set_sensitive(cell, TRUE);
    }
}

void game_grid_set_cell_clicked_callback(GameGrid* grid, GCallback callback, gpointer data) {
    g_return_if_fail(GAME_IS_GRID(grid));

    grid->cell_clicked_callback = callback;
    grid->callback_data = data;
}