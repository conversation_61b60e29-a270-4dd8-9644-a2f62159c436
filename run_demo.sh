#!/bin/bash

# <PERSON>ript to run the MultiApp Suite Demo

echo "=== MultiApp Suite Demo Launcher ==="
echo ""

# Check if the demo executable exists
if [ ! -f "simple_demo" ]; then
    echo "Demo executable not found. Building..."
    
    # Check if GTK3 is available
    if ! pkg-config --exists gtk+-3.0; then
        echo "Error: GTK3 development libraries not found."
        echo "Please install with: sudo apt install libgtk-3-dev"
        exit 1
    fi
    
    # Compile the demo
    echo "Compiling demo application..."
    g++ -std=c++17 `pkg-config --cflags gtk+-3.0` -o simple_demo simple_demo.cpp `pkg-config --libs gtk+-3.0`
    
    if [ $? -ne 0 ]; then
        echo "Compilation failed!"
        exit 1
    fi
    
    echo "✓ Demo compiled successfully"
fi

echo ""
echo "Starting MultiApp Suite Demo..."
echo ""
echo "The demo includes:"
echo "  📱 Calculator - Basic calculator with number pad"
echo "  💣 Minesweeper - Classic minesweeper game grid"
echo "  🎵 Music Player - Media player interface"
echo "  🎬 Video Player - Video player interface"
echo ""
echo "Use the tabs at the top to switch between applications."
echo ""

# Run the demo
./simple_demo &
DEMO_PID=$!

echo "✓ Demo started (PID: $DEMO_PID)"
echo ""
echo "The MultiApp Suite Demo window should now be visible."
echo "Close the window or press Ctrl+C to exit."
echo ""

# Wait for the process to finish
wait $DEMO_PID
echo ""
echo "Demo closed."
