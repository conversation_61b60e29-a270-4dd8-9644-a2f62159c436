#include <gtk/gtk.h>
#include <iostream>

// Simple demo application to test the GUI
class SimpleMultiApp {
private:
    GtkApplication* app;
    GtkWidget* main_window;
    GtkWidget* notebook;
    
public:
    SimpleMultiApp() : app(nullptr), main_window(nullptr), notebook(nullptr) {}
    
    void create_calculator_tab() {
        GtkWidget* calc_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 5);
        gtk_container_set_border_width(GTK_CONTAINER(calc_box), 10);
        
        // Display
        GtkWidget* display = gtk_entry_new();
        gtk_entry_set_text(GTK_ENTRY(display), "0");
        gtk_entry_set_alignment(GTK_ENTRY(display), 1.0);
        gtk_widget_set_sensitive(display, FALSE);
        gtk_box_pack_start(GTK_BOX(calc_box), display, FALSE, FALSE, 5);
        
        // Button grid
        GtkWidget* grid = gtk_grid_new();
        gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
        gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
        
        // Add some calculator buttons
        const char* buttons[] = {
            "7", "8", "9", "/",
            "4", "5", "6", "*",
            "1", "2", "3", "-",
            "0", ".", "=", "+"
        };
        
        for (int i = 0; i < 16; i++) {
            GtkWidget* button = gtk_button_new_with_label(buttons[i]);
            gtk_widget_set_size_request(button, 50, 50);
            gtk_grid_attach(GTK_GRID(grid), button, i % 4, i / 4, 1, 1);
        }
        
        gtk_box_pack_start(GTK_BOX(calc_box), grid, TRUE, TRUE, 5);
        
        GtkWidget* calc_label = gtk_label_new("Calculator");
        gtk_notebook_append_page(GTK_NOTEBOOK(notebook), calc_box, calc_label);
    }
    
    void create_minesweeper_tab() {
        GtkWidget* mine_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 5);
        gtk_container_set_border_width(GTK_CONTAINER(mine_box), 10);
        
        // Info bar
        GtkWidget* info_bar = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 10);
        GtkWidget* mines_label = gtk_label_new("Mines: 10");
        GtkWidget* time_label = gtk_label_new("Time: 00:00");
        GtkWidget* reset_button = gtk_button_new_with_label("Reset");
        
        gtk_box_pack_start(GTK_BOX(info_bar), mines_label, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(info_bar), time_label, FALSE, FALSE, 0);
        gtk_box_pack_end(GTK_BOX(info_bar), reset_button, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(mine_box), info_bar, FALSE, FALSE, 5);
        
        // Game grid
        GtkWidget* game_grid = gtk_grid_new();
        gtk_grid_set_row_spacing(GTK_GRID(game_grid), 2);
        gtk_grid_set_column_spacing(GTK_GRID(game_grid), 2);
        
        // Create a simple 8x8 grid
        for (int row = 0; row < 8; row++) {
            for (int col = 0; col < 8; col++) {
                GtkWidget* cell = gtk_button_new();
                gtk_widget_set_size_request(cell, 30, 30);
                gtk_grid_attach(GTK_GRID(game_grid), cell, col, row, 1, 1);
            }
        }
        
        gtk_box_pack_start(GTK_BOX(mine_box), game_grid, TRUE, TRUE, 5);
        
        GtkWidget* mine_label = gtk_label_new("Minesweeper");
        gtk_notebook_append_page(GTK_NOTEBOOK(notebook), mine_box, mine_label);
    }
    
    void create_media_tab(const char* title, const char* type) {
        GtkWidget* media_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 5);
        gtk_container_set_border_width(GTK_CONTAINER(media_box), 10);
        
        // Media area
        GtkWidget* media_area = gtk_drawing_area_new();
        gtk_widget_set_size_request(media_area, 400, 300);
        gtk_box_pack_start(GTK_BOX(media_box), media_area, TRUE, TRUE, 5);
        
        // Controls
        GtkWidget* controls = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 5);
        GtkWidget* play_btn = gtk_button_new_with_label("Play");
        GtkWidget* pause_btn = gtk_button_new_with_label("Pause");
        GtkWidget* stop_btn = gtk_button_new_with_label("Stop");
        GtkWidget* open_btn = gtk_button_new_with_label("Open File");
        
        gtk_box_pack_start(GTK_BOX(controls), play_btn, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(controls), pause_btn, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(controls), stop_btn, FALSE, FALSE, 0);
        gtk_box_pack_end(GTK_BOX(controls), open_btn, FALSE, FALSE, 0);
        
        gtk_box_pack_start(GTK_BOX(media_box), controls, FALSE, FALSE, 5);
        
        // Status
        GtkWidget* status = gtk_label_new("Ready");
        gtk_box_pack_start(GTK_BOX(media_box), status, FALSE, FALSE, 5);
        
        GtkWidget* tab_label = gtk_label_new(title);
        gtk_notebook_append_page(GTK_NOTEBOOK(notebook), media_box, tab_label);
    }
    
    void create_ui() {
        main_window = gtk_application_window_new(app);
        gtk_window_set_title(GTK_WINDOW(main_window), "MultiApp Suite - Demo");
        gtk_window_set_default_size(GTK_WINDOW(main_window), 800, 600);
        
        // Create notebook for tabs
        notebook = gtk_notebook_new();
        gtk_container_add(GTK_CONTAINER(main_window), notebook);
        
        // Create tabs
        create_calculator_tab();
        create_minesweeper_tab();
        create_media_tab("Music Player", "audio");
        create_media_tab("Video Player", "video");
        
        gtk_widget_show_all(main_window);
    }
    
    static void activate(GtkApplication* app, gpointer user_data) {
        SimpleMultiApp* demo = static_cast<SimpleMultiApp*>(user_data);
        demo->app = app;
        demo->create_ui();
    }
    
    int run(int argc, char** argv) {
        app = gtk_application_new("org.multiapp.demo", G_APPLICATION_FLAGS_NONE);
        g_signal_connect(app, "activate", G_CALLBACK(activate), this);
        
        int status = g_application_run(G_APPLICATION(app), argc, argv);
        g_object_unref(app);
        
        return status;
    }
};

int main(int argc, char** argv) {
    SimpleMultiApp demo;
    return demo.run(argc, argv);
}
