#!/bin/bash

# MultiApp Suite Launcher Script

echo "🚀 MultiApp Suite Launcher"
echo "=========================="
echo ""

# Check if executable exists
if [ ! -f "multiapp-suite" ]; then
    echo "Building MultiApp Suite..."
    
    # Check GTK3 availability
    if ! pkg-config --exists gtk+-3.0; then
        echo "❌ Error: GTK3 development libraries not found"
        echo "Please install with: sudo apt install libgtk-3-dev"
        exit 1
    fi
    
    # Compile
    echo "Compiling application..."
    g++ -std=c++17 `pkg-config --cflags gtk+-3.0` -o multiapp-suite multiapp_main.cpp `pkg-config --libs gtk+-3.0`
    
    if [ $? -ne 0 ]; then
        echo "❌ Compilation failed!"
        exit 1
    fi
    
    echo "✅ Build successful!"
fi

echo ""
echo "🎯 Starting MultiApp Suite..."
echo ""
echo "Features:"
echo "  🧮 Calculator - Full-featured calculator with scientific functions"
echo "  💣 Minesweeper - Classic minesweeper game with 10x10 grid"
echo "  🎵 Music Player - Audio player with media controls"
echo "  🎬 Video Player - Video player with playback controls"
echo ""
echo "💡 Use the tabs to switch between applications"
echo "🔄 Close the window to exit"
echo ""

# Launch the application
./multiapp-suite

echo ""
echo "👋 MultiApp Suite closed. Thank you for using our application!"
