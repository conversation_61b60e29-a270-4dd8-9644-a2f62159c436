# Simple Makefile for MultiApp Suite (alternative to CMake)
# This can be used when CMake is not available

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -Wpedantic -Iinclude
LDFLAGS = 

# GTK3 flags (if available)
GTK_CFLAGS = $(shell pkg-config --cflags gtk+-3.0 2>/dev/null)
GTK_LIBS = $(shell pkg-config --libs gtk+-3.0 2>/dev/null)

# GStreamer flags (if available)
GSTREAMER_CFLAGS = $(shell pkg-config --cflags gstreamer-1.0 2>/dev/null)
GSTREAMER_LIBS = $(shell pkg-config --libs gstreamer-1.0 2>/dev/null)

# Test flags removed for production build
# GTEST_CFLAGS = $(shell pkg-config --cflags gtest 2>/dev/null)
# GTEST_LIBS = $(shell pkg-config --libs gtest gtest_main 2>/dev/null)

# Add flags if packages are available
ifneq ($(GTK_CFLAGS),)
    CXXFLAGS += $(GTK_CFLAGS)
    LDFLAGS += $(GTK_LIBS)
endif

ifneq ($(GSTREAMER_CFLAGS),)
    CXXFLAGS += $(GSTREAMER_CFLAGS)
    LDFLAGS += $(GSTREAMER_LIBS)
endif

# Source directories
SRCDIR = src
BUILDDIR = build
OBJDIR = $(BUILDDIR)/obj

# Core sources
CORE_SOURCES = $(wildcard $(SRCDIR)/core/*/*.cpp)
APP_SOURCES = $(wildcard $(SRCDIR)/apps/*/*.cpp)
MAIN_SOURCES = $(wildcard $(SRCDIR)/main/*.cpp)

# Object files
CORE_OBJECTS = $(CORE_SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
APP_OBJECTS = $(APP_SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
MAIN_OBJECTS = $(MAIN_SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)

# Targets
EXECUTABLE = $(BUILDDIR)/multiapp-suite

.PHONY: all clean install help

all: $(EXECUTABLE)

# Create build directories
$(OBJDIR):
	mkdir -p $(OBJDIR)/core/gtk $(OBJDIR)/core/json $(OBJDIR)/core/utils $(OBJDIR)/core/config
	mkdir -p $(OBJDIR)/apps/calculator $(OBJDIR)/apps/minesweeper $(OBJDIR)/apps/music_player $(OBJDIR)/apps/video_player
	mkdir -p $(OBJDIR)/main

# Compile core objects
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Link main executable
$(EXECUTABLE): $(CORE_OBJECTS) $(APP_OBJECTS) $(MAIN_OBJECTS)
	$(CXX) $^ $(LDFLAGS) -o $@

# Clean build files
clean:
	rm -rf $(BUILDDIR)

# Install (basic)
install: $(EXECUTABLE)
	@echo "Installing MultiApp Suite..."
	@echo "Note: This requires sudo access for system installation"
	@echo "Executable built at: $(EXECUTABLE)"

# Help
help:
	@echo "MultiApp Suite Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all      - Build the main application"
	@echo "  clean    - Remove build files"
	@echo "  install  - Install the application"
	@echo "  help     - Show this help"
	@echo ""
	@echo "Requirements:"
	@echo "  - GTK3 development packages"
	@echo "  - GStreamer development packages"
	@echo ""
	@echo "Install dependencies with:"
	@echo "  sudo apt install cmake build-essential pkg-config libgtk-3-dev libgstreamer1.0-dev"
