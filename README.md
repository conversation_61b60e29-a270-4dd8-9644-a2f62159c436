# MultiAppV1 - Linux Mint Multi-App Suite

A comprehensive multi-application suite for Linux Mint featuring a calculator, Minesweeper game, music player, and video player. Built with GTK and C++ for optimal performance and broad Linux compatibility.

## Project Overview

This project delivers a unified application suite with four distinct applications, all accessible through a common launcher interface. The suite is designed for efficiency, maintainability, and user experience.

## Architecture

### Four-Phase Development Approach

```mermaid
graph TD
    A[Phase 1: Setup & Architecture] --> B[Phase 2: Core Framework]
    B --> C[Phase 3: App Development]
    C --> D[Phase 4: Integration & Testing]

    A --> A1[Agent 1: Project Structure]
    A --> A2[Agent 2: Architecture Design]
    A --> A3[Agent 3: Dependencies]
    A --> A4[Agent 4: GTK Framework]

    B --> B1[Agent 1: Shared Components]
    B --> B2[Agent 2: JSON Database]
    B --> B3[Agent 3: Utilities]
    B --> B4[Agent 4: UI Framework]

    C --> C1[Agent 1: Calculator]
    C --> C2[Agent 2: Minesweeper]
    C --> C3[Agent 3: Music Player]
    C --> C4[Agent 4: Video Player]

    D --> D1[Agent 1: Integration]
    D --> D2[Agent 2: Testing]
    D --> D3[Agent 3: Optimization]
    D --> D4[Agent 4: Documentation]
```

### Application Structure

```mermaid
graph TB
    subgraph "Main Application"
        Launcher[App Launcher]
        Config[Configuration Manager]
    end

    subgraph "Core Framework"
        GTK[GTK Interface Layer]
        JSON[JSON Database]
        Utils[Shared Utilities]
    end

    subgraph "Applications"
        Calc[Calculator App]
        Mine[Minesweeper Game]
        Music[Music Player]
        Video[Video Player]
    end

    Launcher --> GTK
    Launcher --> Config
    GTK --> JSON
    GTK --> Utils
    Calc --> GTK
    Mine --> GTK
    Music --> GTK
    Video --> GTK
```

## Technical Specifications

### Requirements
- **Language**: C++ (C++17 standard)
- **GUI Framework**: GTK 3.0+
- **Build System**: CMake 3.16+
- **Dependencies**:
  - GTK 3.0 development libraries
  - GStreamer (for media players)
  - nlohmann/json (header-only JSON library)
  - Standard C++17 libraries

### Platform Support
- Linux Mint (primary target)
- Ubuntu/Debian derivatives
- Other GTK-supported Linux distributions

## Phase 1: Project Setup and Architecture Design

### Agent 1: Project Structure and Build System
- Create directory structure
- Set up CMake build system
- Configure compiler flags and optimization
- Implement automated build scripts

### Agent 2: Architecture Design and Documentation
- Design overall application architecture
- Create detailed API specifications
- Document inter-app communication protocols
- Design shared component interfaces

### Agent 3: Dependency Management and Environment Setup
- Set up development environment
- Configure package management
- Create dependency installation scripts
- Set up CI/CD pipeline configuration

### Agent 4: Initial GTK Framework Setup
- Initialize GTK application structure
- Create basic window management
- Implement application lifecycle management
- Set up GTK event handling framework

## Phase 2: Core GTK Framework and Shared Components

### Agent 1: Shared GTK Components
- Custom GTK widgets (calculator buttons, game grids)
- Reusable dialog components
- Common toolbar implementations
- Standardized menu systems

### Agent 2: JSON Database Implementation
- JSON schema design for app data
- Database abstraction layer
- CRUD operations for app data
- Data validation and error handling

### Agent 3: Common Utilities and Helpers
- String manipulation utilities
- File I/O helpers
- Math utilities for calculator
- Configuration management

### Agent 4: UI Theme and Styling Framework
- GTK CSS styling framework
- Theme management system
- Responsive layout utilities
- Accessibility compliance

## Phase 3: Individual App Development

### Agent 1: Calculator Application
- Basic arithmetic operations
- Scientific calculator functions
- Expression parsing and evaluation
- History tracking with JSON storage

### Agent 2: Minesweeper Game
- Game logic implementation
- GTK grid-based UI
- Difficulty levels and scoring
- Game state persistence

### Agent 3: Music Player
- Audio file format support
- Playlist management
- Playback controls (play, pause, skip)
- Volume and equalizer controls

### Agent 4: Video Player
- Video file format support
- GTK video widget integration
- Playback controls and seeking
- Fullscreen and windowed modes

## Phase 4: Integration, Testing, and Polish

### Agent 1: App Integration and Launcher
- Unified application launcher
- Inter-app communication system
- Shared resource management
- Application switching logic

### Agent 2: Quality Assurance and Optimization
- Code quality assurance
- Performance optimization
- Memory usage optimization
- User experience validation

### Agent 3: Performance Optimization
- Memory usage optimization
- CPU performance tuning
- GTK rendering optimization
- Database query optimization

### Agent 4: Documentation and Packaging
- User documentation
- API documentation
- Installation and build guides
- Package creation for distribution

## Development Workflow

### Parallel Development Strategy
Each phase will be developed concurrently by specialized agents:

1. **Daily Standup**: Agents coordinate progress and dependencies
2. **Code Review**: Cross-agent code reviews for quality assurance
3. **Integration Testing**: Continuous integration of components
4. **Documentation**: Real-time documentation updates

### Communication Protocol
- Shared Git repository with feature branches
- Daily merge of completed components
- Automated testing on integration
- Weekly milestone reviews

## Success Criteria

- All four applications functional and integrated
- GTK-based UI with consistent theming
- JSON database for data persistence
- No external dependencies beyond GTK and standard libraries
- Performance suitable for Linux Mint systems
- Comprehensive documentation and build instructions

## Building and Running

### Production Build

For a production-ready build with optimizations:

```bash
./build-production.sh
```

This script will:
- Check for required dependencies
- Clean previous builds
- Configure with Release optimizations
- Build the application
- Verify the build

### Manual Build

Using CMake:
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
./multiapp-suite
```

Using Make (fallback):
```bash
make clean
make -j$(nproc)
./build/multiapp-suite
```

### Installation

```bash
cd build
sudo make install
```

## Application Features

- **Calculator**: Full-featured calculator with scientific functions
- **Minesweeper**: Classic minesweeper game with multiple difficulty levels
- **Music Player**: Audio playback with playlist support
- **Video Player**: Video playback with media controls
- **Unified Launcher**: Easy access to all applications

## Status

✅ **Production Ready** - All core features implemented and optimized for release.