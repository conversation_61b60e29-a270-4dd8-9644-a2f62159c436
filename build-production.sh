#!/bin/bash

# Production Build Script for MultiApp Suite
# This script builds the application with production optimizations

set -e  # Exit on any error

echo "=== MultiApp Suite Production Build ==="
echo ""

# Check if required dependencies are installed
echo "Checking dependencies..."

# Check for required packages
REQUIRED_PACKAGES=("cmake" "pkg-config" "libgtk-3-dev" "libgstreamer1.0-dev")
MISSING_PACKAGES=()

for package in "${REQUIRED_PACKAGES[@]}"; do
    if ! dpkg -l | grep -q "^ii.*$package"; then
        MISSING_PACKAGES+=("$package")
    fi
done

if [ ${#MISSING_PACKAGES[@]} -ne 0 ]; then
    echo "Error: Missing required packages: ${MISSING_PACKAGES[*]}"
    echo "Please install them with:"
    echo "sudo apt install ${MISSING_PACKAGES[*]}"
    exit 1
fi

echo "✓ All dependencies found"

# Clean previous build
echo ""
echo "Cleaning previous build..."
rm -rf build/
mkdir -p build

# Configure with CMake for Release build
echo ""
echo "Configuring build (Release mode)..."
cd build
cmake .. \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_CXX_FLAGS_RELEASE="-O3 -march=native -flto -DNDEBUG" \
    -DBUILD_TESTS=OFF

# Build the application
echo ""
echo "Building application..."
make -j$(nproc)

# Verify the executable was created
if [ -f "multiapp-suite" ]; then
    echo ""
    echo "✓ Build successful!"
    echo "Executable: $(pwd)/multiapp-suite"
    
    # Show file size and basic info
    echo ""
    echo "Build information:"
    ls -lh multiapp-suite
    echo ""
    
    # Check dependencies
    echo "Runtime dependencies:"
    ldd multiapp-suite | grep -E "(gtk|gstreamer)" || true
    
else
    echo ""
    echo "✗ Build failed - executable not found"
    exit 1
fi

echo ""
echo "=== Production build complete ==="
echo ""
echo "To run the application:"
echo "  cd build && ./multiapp-suite"
echo ""
echo "To install system-wide:"
echo "  sudo make install"
