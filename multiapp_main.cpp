#include <gtk/gtk.h>
#include <iostream>
#include <string>
#include <vector>
#include <cstdlib>
#include <ctime>
#include <cstring>
#include <fstream>
#include <iomanip>
#include <chrono>
#include <sys/stat.h>

// Logging system
class Logger {
private:
    std::string log_dir;
    std::ofstream log_file;

public:
    Logger() {
        // Create logs directory
        log_dir = "multiapp_logs";
        mkdir(log_dir.c_str(), 0755);

        // Create log file with timestamp
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        std::stringstream filename;
        filename << log_dir << "/multiapp_"
                 << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
                 << ".log";

        log_file.open(filename.str(), std::ios::app);
        if (log_file.is_open()) {
            log("SYSTEM", "MultiApp Suite started - Logging initialized");
        }
    }

    ~Logger() {
        if (log_file.is_open()) {
            log("SYSTEM", "MultiApp Suite shutting down");
            log_file.close();
        }
    }

    void log(const std::string& component, const std::string& message) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        std::stringstream timestamp_ss;
        timestamp_ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        std::string timestamp = timestamp_ss.str();

        std::string log_entry = "[" + timestamp + "] [" + component + "] " + message;

        // Write to file
        if (log_file.is_open()) {
            log_file << log_entry << std::endl;
            log_file.flush();
        }

        // Also print to console
        std::cout << log_entry << std::endl;
    }

    void log_error(const std::string& component, const std::string& error) {
        log(component + "_ERROR", "ERROR: " + error);
    }

    void log_click(const std::string& component, const std::string& button) {
        log(component, "Button clicked: " + button);
    }
};

// Simple MultiApp Suite Main Application
class MultiAppSuite {
private:
    GtkApplication* app;
    GtkWidget* main_window;
    GtkWidget* notebook;
    Logger logger;

    // Calculator state
    GtkWidget* calc_display;
    std::string calc_current_value;
    std::string calc_operator;
    double calc_stored_value;
    bool calc_new_number;

    // Minesweeper state
    GtkWidget* mine_grid;
    GtkWidget* mine_counter;
    GtkWidget* mine_timer;
    std::vector<std::vector<GtkWidget*>> mine_buttons;
    std::vector<std::vector<bool>> mine_field;
    std::vector<std::vector<bool>> mine_revealed;
    int mine_count;
    int mine_rows;
    int mine_cols;
    
public:
    MultiAppSuite() : app(nullptr), main_window(nullptr), notebook(nullptr),
                      calc_display(nullptr), calc_current_value("0"), calc_operator(""),
                      calc_stored_value(0.0), calc_new_number(true),
                      mine_grid(nullptr), mine_counter(nullptr), mine_timer(nullptr),
                      mine_count(10), mine_rows(10), mine_cols(10) {
        mine_buttons.resize(mine_rows, std::vector<GtkWidget*>(mine_cols, nullptr));
        mine_field.resize(mine_rows, std::vector<bool>(mine_cols, false));
        mine_revealed.resize(mine_rows, std::vector<bool>(mine_cols, false));

        logger.log("SYSTEM", "MultiAppSuite initialized");
        logger.log("SYSTEM", "Calculator ready");
        logger.log("SYSTEM", "Minesweeper configured: " + std::to_string(mine_rows) + "x" + std::to_string(mine_cols) + " with " + std::to_string(mine_count) + " mines");
        logger.log("SYSTEM", "Media players ready");
    }

    // Calculator button handlers
    static void on_calc_number_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        const char* label = gtk_button_get_label(GTK_BUTTON(button));
        suite->handle_calc_number(label);
    }

    static void on_calc_operator_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        const char* label = gtk_button_get_label(GTK_BUTTON(button));
        suite->handle_calc_operator(label);
    }

    static void on_calc_clear_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        suite->handle_calc_clear();
    }

    static void on_calc_equals_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        suite->handle_calc_equals();
    }

    void handle_calc_number(const char* num) {
        logger.log_click("CALCULATOR", std::string("Number: ") + num);

        if (calc_new_number) {
            calc_current_value = num;
            calc_new_number = false;
            logger.log("CALCULATOR", "Started new number: " + calc_current_value);
        } else {
            if (strcmp(num, ".") == 0 && calc_current_value.find('.') != std::string::npos) {
                logger.log("CALCULATOR", "Ignored duplicate decimal point");
                return; // Don't add multiple decimal points
            }
            calc_current_value += num;
            logger.log("CALCULATOR", "Updated number: " + calc_current_value);
        }
        gtk_entry_set_text(GTK_ENTRY(calc_display), calc_current_value.c_str());
    }

    void handle_calc_operator(const char* op) {
        logger.log_click("CALCULATOR", std::string("Operator: ") + op);

        if (!calc_operator.empty()) {
            logger.log("CALCULATOR", "Chaining operation, calculating previous result");
            handle_calc_equals();
        }

        try {
            calc_stored_value = std::stod(calc_current_value);
            calc_operator = op;
            calc_new_number = true;
            logger.log("CALCULATOR", "Set operator '" + std::string(op) + "' with stored value: " + std::to_string(calc_stored_value));
        } catch (const std::exception& e) {
            logger.log_error("CALCULATOR", "Failed to parse number: " + calc_current_value);
        }
    }

    void handle_calc_clear() {
        logger.log_click("CALCULATOR", "Clear");
        calc_current_value = "0";
        calc_operator = "";
        calc_stored_value = 0.0;
        calc_new_number = true;
        gtk_entry_set_text(GTK_ENTRY(calc_display), "0");
        logger.log("CALCULATOR", "Calculator cleared");
    }

    void handle_calc_equals() {
        logger.log_click("CALCULATOR", "Equals");

        if (calc_operator.empty()) {
            logger.log("CALCULATOR", "No operator set, ignoring equals");
            return;
        }

        try {
            double current = std::stod(calc_current_value);
            double result = calc_stored_value;

            logger.log("CALCULATOR", "Calculating: " + std::to_string(calc_stored_value) + " " + calc_operator + " " + std::to_string(current));

            if (calc_operator == "+") result += current;
            else if (calc_operator == "−") result -= current;
            else if (calc_operator == "×") result *= current;
            else if (calc_operator == "÷") {
                if (current != 0) {
                    result /= current;
                } else {
                    logger.log_error("CALCULATOR", "Division by zero attempted");
                    gtk_entry_set_text(GTK_ENTRY(calc_display), "Error");
                    return;
                }
            }

            calc_current_value = std::to_string(result);
            // Remove trailing zeros
            calc_current_value.erase(calc_current_value.find_last_not_of('0') + 1, std::string::npos);
            calc_current_value.erase(calc_current_value.find_last_not_of('.') + 1, std::string::npos);

            gtk_entry_set_text(GTK_ENTRY(calc_display), calc_current_value.c_str());
            logger.log("CALCULATOR", "Result: " + calc_current_value);

            calc_operator = "";
            calc_new_number = true;
        } catch (const std::exception& e) {
            logger.log_error("CALCULATOR", "Calculation error: " + std::string(e.what()));
            gtk_entry_set_text(GTK_ENTRY(calc_display), "Error");
        }
    }

    // Minesweeper handlers
    static void on_mine_cell_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);

        // Find which button was clicked
        for (int row = 0; row < suite->mine_rows; row++) {
            for (int col = 0; col < suite->mine_cols; col++) {
                if (suite->mine_buttons[row][col] == button) {
                    suite->handle_mine_cell_click(row, col);
                    return;
                }
            }
        }
    }

    static void on_mine_reset_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        suite->reset_minesweeper();
    }

    void handle_mine_cell_click(int row, int col) {
        logger.log_click("MINESWEEPER", "Cell [" + std::to_string(row) + "," + std::to_string(col) + "]");

        if (mine_revealed[row][col]) {
            logger.log("MINESWEEPER", "Cell already revealed, ignoring");
            return;
        }

        mine_revealed[row][col] = true;

        if (mine_field[row][col]) {
            // Hit a mine!
            logger.log("MINESWEEPER", "BOOM! Mine hit at [" + std::to_string(row) + "," + std::to_string(col) + "]");
            gtk_button_set_label(GTK_BUTTON(mine_buttons[row][col]), "💥");
            GtkStyleContext* context = gtk_widget_get_style_context(mine_buttons[row][col]);
            gtk_style_context_add_class(context, "destructive-action");

            // Reveal all mines
            logger.log("MINESWEEPER", "Game over - revealing all mines");
            for (int r = 0; r < mine_rows; r++) {
                for (int c = 0; c < mine_cols; c++) {
                    if (mine_field[r][c]) {
                        gtk_button_set_label(GTK_BUTTON(mine_buttons[r][c]), "💣");
                    }
                }
            }
        } else {
            // Count adjacent mines
            int adjacent = count_adjacent_mines(row, col);
            if (adjacent > 0) {
                char label[4];
                snprintf(label, sizeof(label), "%d", adjacent);
                gtk_button_set_label(GTK_BUTTON(mine_buttons[row][col]), label);

                // Color code the numbers
                GtkStyleContext* context = gtk_widget_get_style_context(mine_buttons[row][col]);
                if (adjacent == 1) gtk_style_context_add_class(context, "suggested-action");
                else if (adjacent >= 3) gtk_style_context_add_class(context, "destructive-action");
            } else {
                gtk_button_set_label(GTK_BUTTON(mine_buttons[row][col]), "");
                // Auto-reveal adjacent empty cells
                reveal_adjacent_empty(row, col);
            }
            gtk_widget_set_sensitive(mine_buttons[row][col], FALSE);
        }
    }

    int count_adjacent_mines(int row, int col) {
        int count = 0;
        for (int r = row - 1; r <= row + 1; r++) {
            for (int c = col - 1; c <= col + 1; c++) {
                if (r >= 0 && r < mine_rows && c >= 0 && c < mine_cols &&
                    !(r == row && c == col) && mine_field[r][c]) {
                    count++;
                }
            }
        }
        return count;
    }

    void reveal_adjacent_empty(int row, int col) {
        for (int r = row - 1; r <= row + 1; r++) {
            for (int c = col - 1; c <= col + 1; c++) {
                if (r >= 0 && r < mine_rows && c >= 0 && c < mine_cols &&
                    !(r == row && c == col) && !mine_revealed[r][c] && !mine_field[r][c]) {
                    handle_mine_cell_click(r, c);
                }
            }
        }
    }

    void reset_minesweeper() {
        logger.log_click("MINESWEEPER", "New Game");
        logger.log("MINESWEEPER", "Resetting game state");

        // Reset all state
        for (int r = 0; r < mine_rows; r++) {
            for (int c = 0; c < mine_cols; c++) {
                mine_field[r][c] = false;
                mine_revealed[r][c] = false;
                gtk_button_set_label(GTK_BUTTON(mine_buttons[r][c]), "");
                gtk_widget_set_sensitive(mine_buttons[r][c], TRUE);

                // Remove style classes
                GtkStyleContext* context = gtk_widget_get_style_context(mine_buttons[r][c]);
                gtk_style_context_remove_class(context, "suggested-action");
                gtk_style_context_remove_class(context, "destructive-action");
            }
        }

        // Place mines randomly
        srand(time(nullptr));
        int mines_placed = 0;
        logger.log("MINESWEEPER", "Placing " + std::to_string(mine_count) + " mines randomly");

        while (mines_placed < mine_count) {
            int r = rand() % mine_rows;
            int c = rand() % mine_cols;
            if (!mine_field[r][c]) {
                mine_field[r][c] = true;
                mines_placed++;
                logger.log("MINESWEEPER", "Mine placed at [" + std::to_string(r) + "," + std::to_string(c) + "]");
            }
        }

        logger.log("MINESWEEPER", "New game ready with " + std::to_string(mines_placed) + " mines");
    }

    // Media player handlers
    static void on_media_open_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        suite->handle_media_open();
    }

    static void on_media_play_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        suite->handle_media_play();
    }

    static void on_media_pause_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        suite->handle_media_pause();
    }

    static void on_media_stop_clicked(GtkWidget* button, gpointer data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(data);
        suite->handle_media_stop();
    }

    void handle_media_open() {
        logger.log_click("MEDIA_PLAYER", "Open File");

        try {
            GtkWidget* dialog = gtk_file_chooser_dialog_new(
                "Open Media File",
                GTK_WINDOW(main_window),
                GTK_FILE_CHOOSER_ACTION_OPEN,
                "_Cancel", GTK_RESPONSE_CANCEL,
                "_Open", GTK_RESPONSE_ACCEPT,
                NULL);

            // Add file filters
            GtkFileFilter* filter = gtk_file_filter_new();
            gtk_file_filter_set_name(filter, "Media Files");
            gtk_file_filter_add_pattern(filter, "*.mp3");
            gtk_file_filter_add_pattern(filter, "*.mp4");
            gtk_file_filter_add_pattern(filter, "*.avi");
            gtk_file_filter_add_pattern(filter, "*.wav");
            gtk_file_filter_add_pattern(filter, "*.ogg");
            gtk_file_filter_add_pattern(filter, "*.mkv");
            gtk_file_filter_add_pattern(filter, "*.flv");
            gtk_file_chooser_add_filter(GTK_FILE_CHOOSER(dialog), filter);

            logger.log("MEDIA_PLAYER", "File dialog opened");

            if (gtk_dialog_run(GTK_DIALOG(dialog)) == GTK_RESPONSE_ACCEPT) {
                char* filename = gtk_file_chooser_get_filename(GTK_FILE_CHOOSER(dialog));
                logger.log("MEDIA_PLAYER", "File selected: " + std::string(filename));

                // Here you would normally load the media file with GStreamer
                // For now, just log the selection
                logger.log("MEDIA_PLAYER", "Media file ready for playback: " + std::string(filename));

                g_free(filename);
            } else {
                logger.log("MEDIA_PLAYER", "File selection cancelled");
            }

            gtk_widget_destroy(dialog);
        } catch (const std::exception& e) {
            logger.log_error("MEDIA_PLAYER", "Error opening file dialog: " + std::string(e.what()));
        }
    }

    void handle_media_play() {
        logger.log_click("MEDIA_PLAYER", "Play");
        logger.log("MEDIA_PLAYER", "Starting media playback (GStreamer integration needed for actual playback)");
        // Here you would implement actual media playback with GStreamer
        // gst_element_set_state(pipeline, GST_STATE_PLAYING);
    }

    void handle_media_pause() {
        logger.log_click("MEDIA_PLAYER", "Pause");
        logger.log("MEDIA_PLAYER", "Pausing media playback");
        // Here you would implement pause functionality
        // gst_element_set_state(pipeline, GST_STATE_PAUSED);
    }

    void handle_media_stop() {
        logger.log_click("MEDIA_PLAYER", "Stop");
        logger.log("MEDIA_PLAYER", "Stopping media playback");
        // Here you would implement stop functionality
        // gst_element_set_state(pipeline, GST_STATE_NULL);
    }
    
    void create_calculator_tab() {
        GtkWidget* calc_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 10);
        gtk_container_set_border_width(GTK_CONTAINER(calc_box), 15);
        
        // Title
        GtkWidget* title = gtk_label_new(NULL);
        gtk_label_set_markup(GTK_LABEL(title), "<span size='large' weight='bold'>Calculator</span>");
        gtk_box_pack_start(GTK_BOX(calc_box), title, FALSE, FALSE, 10);
        
        // Display
        calc_display = gtk_entry_new();
        gtk_entry_set_text(GTK_ENTRY(calc_display), "0");
        gtk_entry_set_alignment(GTK_ENTRY(calc_display), 1.0);
        gtk_widget_set_sensitive(calc_display, FALSE);
        gtk_widget_set_size_request(calc_display, 300, 40);
        gtk_box_pack_start(GTK_BOX(calc_box), calc_display, FALSE, FALSE, 10);
        
        // Button grid
        GtkWidget* grid = gtk_grid_new();
        gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
        gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
        gtk_widget_set_halign(grid, GTK_ALIGN_CENTER);
        
        const char* buttons[] = {
            "C", "±", "%", "÷",
            "7", "8", "9", "×",
            "4", "5", "6", "−",
            "1", "2", "3", "+",
            "0", ".", "=", ""
        };
        
        // Process all buttons including the equals sign
        for (int i = 0; i < 20; i++) {  // Changed from 19 to 20 to include all buttons
            if (i < 19 && strlen(buttons[i]) > 0) {  // Check bounds and non-empty
                GtkWidget* button = gtk_button_new_with_label(buttons[i]);
                gtk_widget_set_size_request(button, 60, 50);

                logger.log("CALCULATOR", "Creating button: " + std::string(buttons[i]));

                // Connect button handlers
                if (strcmp(buttons[i], "C") == 0) {
                    g_signal_connect(button, "clicked", G_CALLBACK(on_calc_clear_clicked), this);
                } else if (strcmp(buttons[i], "=") == 0) {
                    g_signal_connect(button, "clicked", G_CALLBACK(on_calc_equals_clicked), this);
                    GtkStyleContext* context = gtk_widget_get_style_context(button);
                    gtk_style_context_add_class(context, "suggested-action");
                } else if (strchr("÷×−+", buttons[i][0])) {
                    g_signal_connect(button, "clicked", G_CALLBACK(on_calc_operator_clicked), this);
                    GtkStyleContext* context = gtk_widget_get_style_context(button);
                    gtk_style_context_add_class(context, "suggested-action");
                } else if (strchr("0123456789.", buttons[i][0])) {
                    g_signal_connect(button, "clicked", G_CALLBACK(on_calc_number_clicked), this);
                }

                int col = i % 4;
                int row = i / 4;
                if (i == 16) { // "0" button spans 2 columns
                    gtk_grid_attach(GTK_GRID(grid), button, col, row, 2, 1);
                } else {
                    gtk_grid_attach(GTK_GRID(grid), button, col, row, 1, 1);
                }
            }
        }
        
        gtk_box_pack_start(GTK_BOX(calc_box), grid, TRUE, TRUE, 10);
        
        GtkWidget* calc_label = gtk_label_new("🧮 Calculator");
        gtk_notebook_append_page(GTK_NOTEBOOK(notebook), calc_box, calc_label);
    }
    
    void create_minesweeper_tab() {
        GtkWidget* mine_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 10);
        gtk_container_set_border_width(GTK_CONTAINER(mine_box), 15);
        
        // Title
        GtkWidget* title = gtk_label_new(NULL);
        gtk_label_set_markup(GTK_LABEL(title), "<span size='large' weight='bold'>Minesweeper</span>");
        gtk_box_pack_start(GTK_BOX(mine_box), title, FALSE, FALSE, 10);
        
        // Game info
        GtkWidget* info_box = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 20);
        gtk_widget_set_halign(info_box, GTK_ALIGN_CENTER);
        
        GtkWidget* mines_label = gtk_label_new("💣 Mines: 10");
        GtkWidget* time_label = gtk_label_new("⏱️ Time: 00:00");
        GtkWidget* reset_button = gtk_button_new_with_label("🔄 New Game");
        g_signal_connect(reset_button, "clicked", G_CALLBACK(on_mine_reset_clicked), this);

        gtk_box_pack_start(GTK_BOX(info_box), mines_label, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(info_box), time_label, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(info_box), reset_button, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(mine_box), info_box, FALSE, FALSE, 10);
        
        // Game grid
        GtkWidget* game_grid = gtk_grid_new();
        gtk_grid_set_row_spacing(GTK_GRID(game_grid), 2);
        gtk_grid_set_column_spacing(GTK_GRID(game_grid), 2);
        gtk_widget_set_halign(game_grid, GTK_ALIGN_CENTER);
        
        // Create 10x10 grid
        mine_buttons.clear();
        mine_buttons.resize(mine_rows, std::vector<GtkWidget*>(mine_cols));

        for (int row = 0; row < mine_rows; row++) {
            for (int col = 0; col < mine_cols; col++) {
                GtkWidget* cell = gtk_button_new();
                gtk_widget_set_size_request(cell, 25, 25);
                g_signal_connect(cell, "clicked", G_CALLBACK(on_mine_cell_clicked), this);
                gtk_grid_attach(GTK_GRID(game_grid), cell, col, row, 1, 1);
                mine_buttons[row][col] = cell;
            }
        }

        // Initialize the game
        reset_minesweeper();
        
        gtk_box_pack_start(GTK_BOX(mine_box), game_grid, TRUE, TRUE, 10);
        
        GtkWidget* mine_label = gtk_label_new("💣 Minesweeper");
        gtk_notebook_append_page(GTK_NOTEBOOK(notebook), mine_box, mine_label);
    }
    
    void create_media_tab(const char* title, const char* icon, const char* type) {
        GtkWidget* media_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 10);
        gtk_container_set_border_width(GTK_CONTAINER(media_box), 15);
        
        // Title
        GtkWidget* title_widget = gtk_label_new(NULL);
        char markup[256];
        snprintf(markup, sizeof(markup), "<span size='large' weight='bold'>%s</span>", title);
        gtk_label_set_markup(GTK_LABEL(title_widget), markup);
        gtk_box_pack_start(GTK_BOX(media_box), title_widget, FALSE, FALSE, 10);
        
        // Media display area
        GtkWidget* media_frame = gtk_frame_new(NULL);
        GtkWidget* media_area = gtk_drawing_area_new();
        gtk_widget_set_size_request(media_area, 500, 300);
        gtk_container_add(GTK_CONTAINER(media_frame), media_area);
        gtk_box_pack_start(GTK_BOX(media_box), media_frame, TRUE, TRUE, 10);
        
        // Control buttons
        GtkWidget* controls = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 10);
        gtk_widget_set_halign(controls, GTK_ALIGN_CENTER);
        
        GtkWidget* open_btn = gtk_button_new_with_label("📁 Open");
        GtkWidget* play_btn = gtk_button_new_with_label("▶️ Play");
        GtkWidget* pause_btn = gtk_button_new_with_label("⏸️ Pause");
        GtkWidget* stop_btn = gtk_button_new_with_label("⏹️ Stop");

        // Connect button handlers
        g_signal_connect(open_btn, "clicked", G_CALLBACK(on_media_open_clicked), this);
        g_signal_connect(play_btn, "clicked", G_CALLBACK(on_media_play_clicked), this);
        g_signal_connect(pause_btn, "clicked", G_CALLBACK(on_media_pause_clicked), this);
        g_signal_connect(stop_btn, "clicked", G_CALLBACK(on_media_stop_clicked), this);

        gtk_box_pack_start(GTK_BOX(controls), open_btn, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(controls), play_btn, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(controls), pause_btn, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(controls), stop_btn, FALSE, FALSE, 0);
        
        gtk_box_pack_start(GTK_BOX(media_box), controls, FALSE, FALSE, 10);
        
        // Volume and progress
        GtkWidget* progress_box = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 10);
        GtkWidget* volume_label = gtk_label_new("🔊");
        GtkWidget* volume_scale = gtk_scale_new_with_range(GTK_ORIENTATION_HORIZONTAL, 0, 100, 1);
        gtk_widget_set_size_request(volume_scale, 100, -1);
        gtk_range_set_value(GTK_RANGE(volume_scale), 50);
        
        GtkWidget* progress_bar = gtk_progress_bar_new();
        gtk_widget_set_size_request(progress_bar, 300, -1);
        
        gtk_box_pack_start(GTK_BOX(progress_box), volume_label, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(progress_box), volume_scale, FALSE, FALSE, 0);
        gtk_box_pack_start(GTK_BOX(progress_box), progress_bar, TRUE, TRUE, 0);
        gtk_box_pack_start(GTK_BOX(media_box), progress_box, FALSE, FALSE, 10);
        
        // Status
        GtkWidget* status = gtk_label_new("Ready to play media files");
        gtk_box_pack_start(GTK_BOX(media_box), status, FALSE, FALSE, 5);
        
        char tab_label[64];
        snprintf(tab_label, sizeof(tab_label), "%s %s", icon, title);
        GtkWidget* tab_label_widget = gtk_label_new(tab_label);
        gtk_notebook_append_page(GTK_NOTEBOOK(notebook), media_box, tab_label_widget);
    }
    
    void create_ui() {
        main_window = gtk_application_window_new(app);
        gtk_window_set_title(GTK_WINDOW(main_window), "MultiApp Suite");
        gtk_window_set_default_size(GTK_WINDOW(main_window), 900, 700);
        gtk_window_set_position(GTK_WINDOW(main_window), GTK_WIN_POS_CENTER);
        
        // Create main container
        GtkWidget* main_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 0);
        gtk_container_add(GTK_CONTAINER(main_window), main_box);
        
        // Create header bar
        GtkWidget* header = gtk_header_bar_new();
        gtk_header_bar_set_title(GTK_HEADER_BAR(header), "MultiApp Suite");
        gtk_header_bar_set_subtitle(GTK_HEADER_BAR(header), "Production Ready Multi-Application Suite");
        gtk_header_bar_set_show_close_button(GTK_HEADER_BAR(header), TRUE);
        gtk_window_set_titlebar(GTK_WINDOW(main_window), header);
        
        // Create notebook for tabs
        notebook = gtk_notebook_new();
        gtk_notebook_set_tab_pos(GTK_NOTEBOOK(notebook), GTK_POS_TOP);
        gtk_container_add(GTK_CONTAINER(main_box), notebook);
        
        // Create application tabs
        create_calculator_tab();
        create_minesweeper_tab();
        create_media_tab("Music Player", "🎵", "audio");
        create_media_tab("Video Player", "🎬", "video");
        
        gtk_widget_show_all(main_window);
    }
    
    static void activate(GtkApplication* app, gpointer user_data) {
        MultiAppSuite* suite = static_cast<MultiAppSuite*>(user_data);
        suite->app = app;
        suite->create_ui();
    }
    
    int run(int argc, char** argv) {
        app = gtk_application_new("org.multiapp.suite", G_APPLICATION_DEFAULT_FLAGS);
        g_signal_connect(app, "activate", G_CALLBACK(activate), this);
        
        int status = g_application_run(G_APPLICATION(app), argc, argv);
        g_object_unref(app);
        
        return status;
    }
};

int main(int argc, char** argv) {
    std::cout << "Starting MultiApp Suite..." << std::endl;
    MultiAppSuite suite;
    return suite.run(argc, argv);
}
