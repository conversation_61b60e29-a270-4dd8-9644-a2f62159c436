cmake_minimum_required(VERSION 3.16)
project(MultiAppV1 VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra -Wpedantic -fsanitize=address")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -march=native -flto")

# Find required packages
find_package(PkgConfig REQUIRED)

# GTK 3.0
pkg_check_modules(GTK3 REQUIRED gtk+-3.0)
include_directories(${GTK3_INCLUDE_DIRS})
link_directories(${GTK3_LIBRARY_DIRS})
add_definitions(${GTK3_CFLAGS_OTHER})

# GStreamer
pkg_check_modules(GSTREAMER REQUIRED gstreamer-1.0)
include_directories(${GSTREAMER_INCLUDE_DIRS})
link_directories(${GSTREAMER_LIBRARY_DIRS})
add_definitions(${GSTREAMER_CFLAGS_OTHER})

# nlohmann/json (header-only)
include_directories(external/nlohmann)

# Include directories
include_directories(include)

# Add subdirectories
add_subdirectory(src/core)
add_subdirectory(src/apps)
add_subdirectory(src/main)

# Tests disabled for production build
# option(BUILD_TESTS "Build test suite" OFF)
# if(BUILD_TESTS)
#     enable_testing()
#     add_subdirectory(tests)
# endif()

# Installation
install(TARGETS multiapp-suite DESTINATION bin)
install(DIRECTORY resources/icons/ DESTINATION share/icons/hicolor/scalable/apps FILES_MATCHING PATTERN "*.svg")
install(FILES resources/multiapp-suite.desktop DESTINATION share/applications)

# Package configuration
set(CPACK_PACKAGE_NAME "multiapp-suite")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION "Multi-application suite for Linux Mint")
set(CPACK_PACKAGE_CONTACT "MultiApp Team")
set(CPACK_DEBIAN_PACKAGE_DEPENDS "libgtk-3-0 (>= 3.24), libgstreamer1.0-0 (>= 1.16)")
set(CPACK_DEBIAN_PACKAGE_SECTION "utils")
set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")

include(CPack)